#!/usr/bin/env node

/**
 * Test script to verify demo pages exclusion functionality
 * 
 * This script tests that:
 * 1. Demo pages return 404 when PUBLIC_ENABLE_DEMO_PAGES=false
 * 2. Demo pages work normally when PUBLIC_ENABLE_DEMO_PAGES=true
 * 3. Essential pages always work regardless of the setting
 */

import { execSync } from 'child_process';
import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

const DEMO_PAGES = [
  '/blog/',
  '/pricing/',
  '/features/',
  '/career/',
  '/case-study/',
  '/changelog/',
  '/company/',
  '/example-with-ads/',
  '/faqs/',
  '/integrations/',
  '/reviews/',
  '/test-cookie-consent/'
];

const ESSENTIAL_PAGES = [
  '/',
  '/about/',
  '/contact/',
  '/rest-areas/',
  '/privacy-policy/',
  '/terms-of-service/'
];

function updateEnvVariable(value) {
  const envPath = '.env';
  let envContent = readFileSync(envPath, 'utf8');
  
  // Update the PUBLIC_ENABLE_DEMO_PAGES value
  envContent = envContent.replace(
    /PUBLIC_ENABLE_DEMO_PAGES=.*/,
    `PUBLIC_ENABLE_DEMO_PAGES=${value}`
  );
  
  writeFileSync(envPath, envContent);
  console.log(`✓ Set PUBLIC_ENABLE_DEMO_PAGES=${value}`);
}

function buildProject() {
  console.log('Building project...');
  try {
    execSync('npm run build', { stdio: 'pipe' });
    console.log('✓ Build completed successfully');
  } catch (error) {
    console.error('✗ Build failed:', error.message);
    process.exit(1);
  }
}

function checkPageContent(pagePath, shouldBe404 = false) {
  const filePath = join('dist', pagePath, 'index.html');
  
  try {
    const content = readFileSync(filePath, 'utf8');
    const is404 = content.includes('404 - Page Not Found');
    
    if (shouldBe404 && is404) {
      console.log(`✓ ${pagePath} correctly returns 404`);
      return true;
    } else if (!shouldBe404 && !is404) {
      console.log(`✓ ${pagePath} correctly loads normally`);
      return true;
    } else {
      console.log(`✗ ${pagePath} unexpected state - is404: ${is404}, shouldBe404: ${shouldBe404}`);
      return false;
    }
  } catch (error) {
    console.log(`✗ ${pagePath} file not found or error reading: ${error.message}`);
    return false;
  }
}

function testConfiguration(enableDemoPages, description) {
  console.log(`\n=== Testing: ${description} ===`);
  
  // Update environment variable
  updateEnvVariable(enableDemoPages);
  
  // Build project
  buildProject();
  
  let allTestsPassed = true;
  
  // Test demo pages
  console.log('\nTesting demo pages:');
  for (const page of DEMO_PAGES) {
    const shouldBe404 = !enableDemoPages;
    if (!checkPageContent(page, shouldBe404)) {
      allTestsPassed = false;
    }
  }
  
  // Test essential pages (should always work)
  console.log('\nTesting essential pages:');
  for (const page of ESSENTIAL_PAGES) {
    if (!checkPageContent(page, false)) {
      allTestsPassed = false;
    }
  }
  
  return allTestsPassed;
}

function main() {
  console.log('🧪 Testing Demo Pages Exclusion Functionality\n');
  
  let allTestsPassed = true;
  
  // Test 1: Demo pages disabled (production mode)
  if (!testConfiguration('false', 'Demo pages disabled (production)')) {
    allTestsPassed = false;
  }
  
  // Test 2: Demo pages enabled (development mode)
  if (!testConfiguration('true', 'Demo pages enabled (development)')) {
    allTestsPassed = false;
  }
  
  // Restore production settings
  updateEnvVariable('false');
  
  console.log('\n=== Test Results ===');
  if (allTestsPassed) {
    console.log('🎉 All tests passed! Demo pages exclusion is working correctly.');
    process.exit(0);
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

main();
