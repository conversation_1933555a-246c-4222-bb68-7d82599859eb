/**
 * Demo Pages Utility
 * 
 * Provides utilities for controlling the visibility of demo/marketing pages
 * in production vs development environments.
 * 
 * Demo pages include: blog, pricing, features, integrations, career, etc.
 * These are typically disabled in production for the stops24.com website.
 */

/**
 * Check if demo pages should be enabled
 * @returns {boolean} True if demo pages should be rendered
 */
export function isDemoPageEnabled(): boolean {
  // In development, enable demo pages by default unless explicitly disabled
  const isProduction = import.meta.env.PROD;
  const enableDemoPages = import.meta.env.PUBLIC_ENABLE_DEMO_PAGES;
  
  // If environment variable is explicitly set, use that value
  if (enableDemoPages !== undefined) {
    return enableDemoPages === 'true';
  }
  
  // Default behavior: enable in development, disable in production
  return !isProduction;
}

/**
 * List of demo page paths that should be conditionally rendered
 */
export const DEMO_PAGE_PATHS = [
  '/blog',
  '/blog/',
  '/career',
  '/career/',
  '/case-study',
  '/case-study/',
  '/changelog',
  '/changelog/',
  '/company',
  '/company/',
  '/example-with-ads',
  '/example-with-ads/',
  '/faqs',
  '/faqs/',
  '/features',
  '/features/',
  '/integrations',
  '/integrations/',
  '/pricing',
  '/pricing/',
  '/reviews',
  '/reviews/',
  '/test-cookie-consent',
  '/test-cookie-consent/',
] as const;

/**
 * Check if a given path is a demo page
 * @param path - The path to check
 * @returns {boolean} True if the path is a demo page
 */
export function isDemoPagePath(path: string): boolean {
  // Normalize path
  const normalizedPath = path.toLowerCase().replace(/\/$/, '') + '/';
  
  return DEMO_PAGE_PATHS.some(demoPath => {
    const normalizedDemoPath = demoPath.toLowerCase();
    return normalizedPath === normalizedDemoPath || 
           normalizedPath.startsWith(normalizedDemoPath);
  });
}

/**
 * Get a 404 response for disabled demo pages
 * @param pageName - Name of the page for the error message
 * @returns {Response} 404 response
 */
export function getDemoPageNotFoundResponse(pageName: string): Response {
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - Stops24</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 2rem;
            background: #f9fafb;
            color: #374151;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #dc2626;
            margin-bottom: 1rem;
        }
        p {
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        a {
            color: #2563eb;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>404 - Page Not Found</h1>
        <p>The ${pageName} page is not available on this website.</p>
        <p><a href="/">← Return to Homepage</a></p>
    </div>
</body>
</html>`;

  return new Response(html, {
    status: 404,
    headers: {
      'Content-Type': 'text/html; charset=utf-8',
    },
  });
}
